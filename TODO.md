# 📋 Luminara Paper优化集成计划

## 📝 当前版本待办事项 1.1.0-PRE1

- [ ] 集成Starlight光照引擎 (0016-Starlight.patch)
- [ ] 集成新的chunk加载系统 (0019-Rewrite-chunk-system.patch)
- [ ] 集成Entity Activation Range 2.0 (0337-Entity-Activation-Range-2.0.patch)
- [ ] 优化光照计算算法 (0016-Starlight.patch相关)
- [ ] 实现异步光照更新 (0016-Starlight.patch相关)
- [ ] 实现并行chunk处理 (0019-Rewrite-chunk-system.patch相关)
- [ ] 实现数据包压缩优化 (0298-Optimize-Network-Manager.patch)
- [ ] 添加数据包限制器 (0694-Add-packet-limiter-config.patch)
- [ ] 集成Velocity压缩和加密 (0707-Use-Velocity-compression-and-cipher-natives.patch)
- [ ] 实现连接节流优化 (0108-Configurable-packet-in-spam-threshold.patch)
- [ ] 添加Proxy Protocol支持 (0823-Add-support-for-Proxy-Protocol.patch)
- [ ] 优化网络数据包处理 (0298-Optimize-Network-Manager.patch)
- [ ] 实现异步网络处理 (0104-Avoid-blocking-on-Network-Manager-creation.patch)

## 🚀 Paper优化

### 1.1.0-PRE2

- [ ] 优化HashMapPalette (0737-Optimize-HashMapPalette.patch)
- [ ] 实现更高效的数据容器 (0087-Optimize-DataBits.patch)
- [ ] 优化BlockPosition缓存 (0690-Manually-inline-methods-in-BlockPosition.patch)
- [ ] 集成ConcurrentHashMap优化 (0238-Use-ConcurrentHashMap-in-JsonList.patch)
- [ ] 添加内存池机制 (自定义实现)
- [ ] 优化对象创建和销毁 (0075-Use-a-Shared-Random-for-Entities.patch)
- [ ] 优化实体数据管理器 (0982-Array-backed-synched-entity-data.patch)
- [ ] 实现缓存优化 (0772-Use-a-CHM-for-StructureTemplate.Pallete-cache.patch)

### 1.1.0-PRE3

- [ ] 优化寻路算法 (0081-EntityPathfindEvent.patch)
- [ ] 集成村民AI优化 (0706-Remove-streams-for-villager-AI.patch)
- [ ] 优化地形生成算法 (0325-Flat-bedrock-generator-settings.patch)
- [ ] 实现结构生成缓存 (0774-Add-missing-structure-set-seed-configs.patch)
- [ ] 添加生物群系优化 (0758-Expose-vanilla-BiomeProvider-from-WorldInfo.patch)
- [ ] 集成世界边界优化 (0118-Bound-Treasure-Maps-to-World-Border.patch)
- [ ] 优化爆炸计算 (0040-Optimize-explosions.patch)
- [ ] 优化碰撞检测 (0739-Highly-optimise-single-and-multi-AABB-VoxelShapes-an.patch)

### 1.1.0-PRE4

- [ ] 添加更多实体API (0033-Entity-Origin-API.patch, 0079-Entity-AddTo-RemoveFrom-World-Events.patch)
- [ ] 实现高级世界API (0071-Add-World-Util-Methods.patch, 0215-Implement-World.getEntity-UUID-API.patch)
- [ ] 集成玩家数据API (0141-Basic-PlayerProfile-API.patch, 0182-Player.setPlayerProfile-API.patch)
- [ ] 优化事件调用性能 (0078-Only-process-BlockPhysicsEvent-if-a-plugin-has-a-lis.patch)
- [ ] 实现事件优先级优化 (自定义实现)
- [ ] 集成自定义事件系统 (0045-0200系列多个事件patch)
- [ ] 添加Brigadier API支持 (0295-Implement-Brigadier-Mojang-API.patch)

### 1.1.0-PRE5

- [ ] 修复chunk加载相关bug (0064-Chunk-Save-Reattempt.patch, 0316-Fix-World-isChunkGenerated-calls.patch)
- [ ] 实现自动恢复功能 (0131-Properly-handle-async-calls-to-restart-the-server.patch)
- [ ] 优化错误处理 (0679-Improve-and-expand-AsyncCatcher.patch)
- [ ] 修复数据同步问题 (0785-Fix-Entity-Position-Desync.patch)

### 1.1.0-PRE6

- [ ] 集成Paper配置文件系统集成到arclight.conf (0005-Paper-config-files.patch)
- [ ] 添加动态配置重载 (0067-Allow-Reloading-of-Custom-Permissions.patch)
- [ ] 添加更多可配置选项 (0020-0100系列多个patch)

## 🔧 Paper API集成计划

### 1.2.0-PRE1 核心系统API
- [ ] 添加Paper工具类 (0006-Paper-Utils.patch)
- [ ] 集成ASM事件执行器 (0007-Use-ASM-for-event-executors.patch)
- [ ] 添加Paper插件系统 (0008-Paper-Plugins.patch)
- [ ] 添加Position API (0009-Add-Position.patch)
- [ ] 集成Timings v2 (0010-Timings-v2.patch)

### 1.2.0-PRE2 实体API
- [ ] 实体Zap事件 (0041-Add-EntityZapEvent.patch)
- [ ] 实体来源于刷怪笼API (0062-Entity-fromMobSpawner.patch)
- [ ] 实体获取区块API (0121-Entity-getChunk-API.patch)
- [ ] 实体变形事件 (0125-EntityTransformedEvent.patch)
- [ ] 实体跳跃API (0184-Entity-Jump-API.patch)
- [ ] 实体是否正在tick (0215-Entity-isTicking.patch)
- [ ] 实体生成原因 (0171-Entity-getEntitySpawnReason.patch)
- [ ] 实体液体API (0204-Add-entity-liquid-API.patch)
- [ ] 实体碰撞API (0372-Collision-API.patch)
- [ ] 实体击退API (0385-Add-entity-knockback-API.patch)
- [ ] 实体身体偏航API (0406-Add-Entity-Body-Yaw-API.patch)

### 1.2.0-PRE3 玩家API
- [ ] 玩家影响生成API (0012-Player-affects-spawning-API.patch)
- [ ] 玩家语言变更事件 (0016-Add-PlayerLocaleChangeEvent.patch)
- [ ] 玩家初始生成事件 (0019-Add-PlayerInitialSpawnEvent.patch)
- [ ] 玩家Tab列表和标题API (0025-Player-Tab-List-and-Title-APIs.patch)
- [ ] 玩家跳跃事件 (0071-Add-PlayerJumpEvent.patch)
- [ ] 玩家盔甲变更事件 (0073-Add-PlayerArmorChangeEvent.patch)
- [ ] 玩家经验拾取事件 (0078-PlayerPickupExperienceEvent.patch)
- [ ] 玩家客户端选项API (0189-Add-Player-Client-Options-API.patch)
- [ ] 玩家攻击冷却重置事件 (0190-Add-PlayerAttackEntityCooldownResetEvent.patch)
- [ ] 玩家配方书点击事件 (0201-Add-and-implement-PlayerRecipeBookClickEvent.patch)
- [ ] 玩家鞘翅推进API (0217-Player-elytra-boost-API.patch)
- [ ] 玩家退出原因API (0222-Add-API-for-quit-reason.patch)
- [ ] 玩家物品冷却事件 (0225-Add-PlayerItemCooldownEvent.patch)
- [ ] 玩家剪切方块事件 (0227-Add-PlayerShearBlockEvent.patch)
- [ ] 玩家区块加载/卸载事件 (0228-Player-Chunk-Load-Unload-Events.patch)
- [ ] 玩家交易事件 (0231-Added-PlayerTradeEvent.patch)
- [ ] 玩家花盆操作事件 (0235-Add-PlayerFlowerPotManipulateEvent.patch)
- [ ] 玩家讲台页面变更事件 (0241-Added-PlayerLecternPageChangeEvent.patch)
- [ ] 玩家织布机图案选择事件 (0242-Added-PlayerLoomPatternSelectEvent.patch)
- [ ] 玩家信标效果变更事件 (0249-Added-PlayerChangeBeaconEffectEvent.patch)
- [ ] 玩家切石机配方选择事件 (0250-Added-PlayerStonecutterRecipeSelectEvent.patch)
- [ ] 玩家命名实体事件 (0261-added-PlayerNameEntityEvent.patch)
- [ ] 玩家深度睡眠事件 (0270-Added-PlayerDeepSleepEvent.patch)
- [ ] 玩家床铺进入失败事件 (0272-Added-PlayerBedFailEnterEvent.patch)
- [ ] 玩家手臂摆动事件 (0300-Adds-PlayerArmSwingEvent.patch)
- [ ] 玩家告示牌命令预处理事件 (0301-Add-PlayerSignCommandPreprocessEvent.patch)
- [ ] 玩家设置重生点事件 (0305-Add-PlayerSetSpawnEvent.patch)
- [ ] 玩家物品框变更事件 (0325-Add-PlayerItemFrameChangeEvent.patch)
- [ ] 玩家库存槽位变更事件 (0380-Add-PlayerInventorySlotChangeEvent.patch)
- [ ] 玩家监守者警告API (0389-Add-Player-Warden-Warning-API.patch)
- [ ] 玩家实体追踪事件 (0395-Player-Entity-Tracking-Events.patch)
- [ ] 玩家拾取物品事件 (0434-Add-PlayerPickItemEvent.patch)
- [ ] 玩家连接状态API (0438-Add-OfflinePlayer-isConnected.patch)

### 1.2.0-PRE4 世界/区块API
- [ ] 视距API (0017-Add-view-distance-API.patch)
- [ ] 异步区块API (0142-Async-Chunks-API.patch)
- [ ] 区块坐标长整型API (0132-Provide-Chunk-Coordinates-as-a-Long-API.patch)
- [ ] 从区块获取瓦片实体 (0133-Ability-to-get-Tile-Entities-from-a-chunk-without-sn.patch)
- [ ] 通过长键访问方块 (0135-Allow-Blocks-to-be-accessed-via-a-long-key.patch)
- [ ] 区块生成检查API (0140-isChunkGenerated-API.patch)
- [ ] 位置区块加载检查 (0099-Location.isChunkLoaded-API.patch)
- [ ] 高度图API (0175-Add-Heightmap-API.patch)
- [ ] 世界边界API (0260-Add-worldborder-events.patch)
- [ ] 更多世界API (0271-More-World-API.patch)
- [ ] 世界键API扩展 (0264-Expand-world-key-API.patch)
- [ ] 生物群系命名空间键方法 (0374-Add-NamespacedKey-biome-methods.patch)
- [ ] 计算生物群系API (0354-Add-getComputedBiome-API.patch)
- [ ] 重新生成区块API (0346-Implement-regenerateChunk.patch)

### 1.2.0-PRE5 事件API
- [ ] 信标效果事件 (0018-Add-BeaconEffectEvent.patch)
- [ ] 异常报告事件 (0022-Add-exception-reporting-event.patch)
- [ ] 投射物碰撞事件 (0045-Add-ProjectileCollideEvent.patch)
- [ ] 非法数据包事件 (0048-IllegalPacketEvent.patch)
- [ ] 玩家传送末地门事件 (0050-PlayerTeleportEndGatewayEvent.patch)
- [ ] 玩家尝试拾取物品事件 (0057-PlayerAttemptPickupItemEvent.patch)
- [ ] 未知命令事件 (0058-Add-UnknownCommandEvent.patch)
- [ ] 资料查找事件 (0063-Profile-Lookup-Events.patch)
- [ ] 资料白名单验证事件 (0068-ProfileWhitelistVerifyEvent.patch)
- [ ] 异步Tab补全事件 (0075-AsyncTabCompleteEvent.patch)
- [ ] 经验球合并事件 (0079-ExperienceOrbMergeEvent.patch)
- [ ] 生物预生成事件 (0081-PreCreatureSpawnEvent.patch)
- [ ] 玩家自然生成生物事件 (0082-PlayerNaturallySpawnCreaturesEvent.patch)
- [ ] 玩家进度标准授予事件 (0085-PlayerAdvancementCriterionGrantEvent.patch)
- [ ] 末影人逃脱事件 (0096-EndermanEscapeEvent.patch)
- [ ] 末影人攻击玩家事件 (0101-EndermanAttackPlayerEvent.patch)
- [ ] 女巫消耗药水事件 (0102-WitchConsumePotionEvent.patch)
- [ ] 女巫投掷药水事件 (0103-WitchThrowPotionEvent.patch)
- [ ] 女巫准备药水事件 (0106-WitchReadyPotionEvent.patch)
- [ ] 实体传送末地门事件 (0108-Add-EntityTeleportEndGatewayEvent.patch)
- [ ] 玩家准备箭矢事件 (0112-PlayerReadyArrowEvent.patch)
- [ ] 实体击退事件 (0113-Add-EntityKnockbackByEntityEvent-and-EntityPushedByE.patch)
- [ ] 玩家鞘翅推进事件 (0123-PlayerElytraBoostEvent.patch)
- [ ] 玩家发射投射物事件 (0124-PlayerLaunchProjectileEvent.patch)
- [ ] 铁砧损坏事件 (0130-AnvilDamageEvent.patch)
- [ ] TNT引爆事件 (0131-Add-TNTPrimeEvent.patch)
- [ ] 史莱姆寻路事件 (0136-Slime-Pathfinder-Events.patch)
- [ ] 幻翼预生成事件 (0137-Add-PhantomPreSpawnEvent.patch)
- [ ] 库存关闭事件原因API (0119-InventoryCloseEvent-Reason-API.patch)
- [ ] 末影龙事件 (0122-EnderDragon-Events.patch)
- [ ] 方块销毁事件 (0167-BlockDestroyEvent.patch)
- [ ] 白名单切换事件 (0168-Add-WhitelistToggleEvent.patch)
- [ ] GS4查询事件 (0169-Add-GS4-Query-event.patch)
- [ ] 玩家重生后事件 (0170-Add-PlayerPostRespawnEvent.patch)
- [ ] 服务器Tick事件 (0173-Server-Tick-Events.patch)
- [ ] 玩家死亡事件物品保留 (0174-PlayerDeathEvent-getItemsToKeep.patch)
- [ ] 玩家死亡事件经验掉落 (0182-PlayerDeathEvent-shouldDropExperience.patch)
- [ ] 鸡蛋孵化事件 (0183-Add-ThrownEggHatchEvent.patch)
- [ ] 村民补货API (0193-Villager-Restocks-API.patch)
- [ ] 生成原因API (0197-Spawn-Reason-API.patch)
- [ ] 准备结果事件 (0205-Add-PrepareResultEvent-PrepareGrindstoneEvent.patch)
- [ ] 铃铛响铃事件 (0207-Add-BellRingEvent.patch)
- [ ] 目标命中事件 (0232-Add-TargetHitEvent-API.patch)
- [ ] 实体加载弩事件 (0237-Add-EntityLoadCrossbowEvent.patch)
- [ ] 世界游戏规则变更事件 (0238-Added-WorldGameRuleChangeEvent.patch)
- [ ] 服务器资源重载事件 (0239-Added-ServerResourcesReloadedEvent.patch)
- [ ] 方块分配失败事件 (0240-Add-BlockFailedDispenseEvent.patch)
- [ ] 方块预分配事件 (0247-Add-BlockPreDispenseEvent.patch)
- [ ] 实体移动事件 (0253-EntityMoveEvent.patch)
- [ ] 龙蛋形成事件 (0252-add-DragonEggFormEvent.patch)
- [ ] 实体内部方块事件 (0288-Add-EntityInsideBlockEvent.patch)
- [ ] 河豚状态变更事件 (0293-Add-PufferFishStateChangeEvent.patch)
- [ ] 铃铛揭示袭击者事件 (0294-Add-BellRevealRaiderEvent.patch)
- [ ] 远古守卫者出现事件 (0295-Add-ElderGuardianAppearanceEvent.patch)
- [ ] 水瓶飞溅事件 (0297-Add-WaterBottleSplashEvent.patch)
- [ ] 实体损坏物品事件 (0306-Added-EntityDamageItemEvent.patch)
- [ ] 方块破坏方块事件 (0309-Add-BlockBreakBlockEvent.patch)
- [ ] 实体传送门退出事件 (0314-add-back-EntityPortalExitEvent.patch)
- [ ] 实体传送门准备事件 (0370-Add-EntityPortalReadyEvent.patch)
- [ ] 实体切换坐下事件 (0386-Added-EntityToggleSitEvent.patch)
- [ ] 玩家攻击实体前事件 (0388-Add-PrePlayerAttackEntityEvent.patch)
- [ ] 方块锁定检查事件 (0398-Add-BlockLockCheckEvent.patch)
- [ ] 实体施肥蛋事件 (0408-Add-EntityFertilizeEggEvent.patch)
- [ ] 堆肥物品事件 (0409-Add-CompostItemEvent-and-EntityCompostItemEvent.patch)
- [ ] 玩家失败移动事件 (0423-Add-PlayerFailMoveEvent.patch)
- [ ] 雕塑催化剂绽放API (0425-SculkCatalyst-bloom-API.patch)
- [ ] 白名单事件 (0421-Add-whitelist-events.patch)

### 1.2.0-PRE6 物品/材料API
- [ ] 完整资源包API (0027-Complete-resource-pack-API.patch)
- [ ] 自定义食物替换 (0031-Custom-replacement-for-eaten-items.patch)
- [ ] 箭矢拾取规则API (0038-Arrow-pickup-rule-API.patch)
- [ ] 战利品表API (0040-LootTable-API.patch)
- [ ] 烟花API (0049-Fireworks-API-s.patch)
- [ ] 物品实体拾取API (0056-Item-canEntityPickup.patch)
- [ ] 物品获取I18N显示名称 (0065-Add-getI18NDisplayName-API.patch)
- [ ] 盔甲架物品Meta (0086-Add-ArmorStand-Item-Meta.patch)
- [ ] 物品堆叠API增强 (0115-ItemStack-API-additions-for-quantity-flags-lore.patch)
- [ ] 物品堆叠配方API (0148-Add-an-API-for-CanPlaceOn-and-CanDestroy-NBT-values.patch)
- [ ] 物品堆叠配方选择 (0150-Add-ItemStackRecipeChoice-Draft-API.patch)
- [ ] 材料API增强 (0152-Material-API-additions.patch)
- [ ] 材料标签 (0153-Add-Material-Tags.patch)
- [ ] 物品堆叠最大使用时间 (0107-ItemStack-getMaxItemUseDuration.patch)
- [ ] 物品堆叠配方API助手 (0166-Add-ItemStack-Recipe-API-helper-methods.patch)
- [ ] 原始字节物品堆叠序列化 (0188-Add-Raw-Byte-ItemStack-Serialization.patch)
- [ ] 物品槽位便利方法 (0192-Add-item-slot-convenience-methods.patch)
- [ ] 物品Meta组件支持 (0202-Support-components-in-ItemMeta.patch)
- [ ] 物品无年龄无玩家拾取 (0220-Item-no-age-no-player-pickup.patch)
- [ ] 销毁速度API (0223-Add-Destroy-Speed-API.patch)
- [ ] 物品实体健康API (0331-Add-API-for-item-entity-health.patch)
- [ ] 物品稀有度API (0265-Item-Rarity-API.patch)
- [ ] 物品堆叠修复检查API (0284-ItemStack-repair-check-API.patch)
- [ ] 物品堆叠编辑Meta (0287-ItemStack-editMeta.patch)
- [ ] 物品默认属性API (0289-Attributes-API-for-item-defaults.patch)
- [ ] 物品堆叠损坏API (0392-ItemStack-damage-API.patch)

### 1.2.0-PRE7 命令API
- [ ] 暴露服务器CommandMap (0020-Expose-server-CommandMap.patch)
- [ ] 命令发送者名称替换 (0029-Add-sender-name-to-commands.yml-replacement.patch)
- [ ] 重载权限命令 (0030-Add-command-to-reload-permissions.yml-and-require-co.patch)
- [ ] 重载命令别名 (0043-Allow-Reloading-of-Command-Aliases.patch)
- [ ] 命令参数空数组修复 (0302-fix-empty-array-elements-in-command-arguments.patch)
- [ ] 更多命令方块API (0312-More-CommandBlock-API.patch)
- [ ] 创建转发反馈的命令发送者API (0345-API-for-creating-command-sender-which-forwards-feedb.patch)
- [ ] 自定义聊天补全建议API (0371-Custom-Chat-Completion-Suggestions-API.patch)

### 1.2.0-PRE8 网络/协议API
- [ ] 暴露客户端协议版本和虚拟主机 (0076-Expose-client-protocol-version-and-virtual-host.patch)
- [ ] 扩展PaperServerListPingEvent (0090-Add-extended-PaperServerListPingEvent.patch)
- [ ] 传统ping支持 (0093-Add-legacy-ping-support-to-PaperServerListPingEvent.patch)
- [ ] 玩家连接关闭事件 (0164-Add-PlayerConnectionCloseEvent.patch)
- [ ] 增加自定义载荷频道消息大小 (0179-Increase-custom-payload-channel-message-size.patch)
- [ ] 品牌支持 (0206-Brand-support.patch)
- [ ] 暴露协议版本 (0266-Expose-protocol-version.patch)
- [ ] 异步玩家预登录事件原始地址 (0279-Add-raw-address-to-AsyncPlayerPreLoginEvent.patch)
- [ ] 获取主机名到异步玩家预登录事件 (0338-Added-getHostname-to-AsyncPlayerPreLoginEvent.patch)

### 1.2.0-PRE9 生物/实体特定API
- [ ] 生活实体箭矢API (0026-Add-methods-for-working-with-arrows-stuck-in-living-.patch)
- [ ] 玩家使用未知实体事件 (0036-Add-PlayerUseUnknownEntityEvent.patch)
- [ ] 实体健康恢复事件快速恢复 (0039-EntityRegainHealthEvent-isFastRegen-API.patch)
- [ ] 盔甲架移动控制API (0047-Add-API-methods-to-control-if-armour-stands-can-move.patch)
- [ ] 经验球API (0052-ExperienceOrbs-API-for-Reason-Source-Triggering-play.patch)
- [ ] 肩膀实体释放API (0061-Shoulder-Entities-Release-API.patch)
- [ ] 改进马鞍API (0064-Improve-the-Saddle-API-for-Horses.patch)
- [ ] 生活实体设置杀手 (0067-LivingEntity-setKiller.patch)
- [ ] 驯服动物获取主人UUID (0088-Tameable-getOwnerUniqueId-API.patch)
- [ ] 末影人随机传送 (0097-Enderman.teleportRandomly.patch)
- [ ] 附近实体API (0098-Additional-world.getNearbyEntities-API-s.patch)
- [ ] 位置附近XXX方法 (0111-Add-getNearbyXXX-methods-to-Location.patch)
- [ ] 生活实体手部举起物品使用API (0116-LivingEntity-Hand-Raised-Item-Use-API.patch)
- [ ] 远程实体API (0117-RangedEntity-API.patch)
- [ ] 设置召唤师 (0120-Allow-setting-the-vex-s-summoner.patch)
- [ ] 禁用盔甲架tick (0126-Allow-disabling-armour-stand-ticking.patch)
- [ ] 骷髅马增强 (0127-SkeletonHorse-Additions.patch)
- [ ] 扩展盔甲架API (0129-Expand-ArmorStand-API.patch)
- [ ] 更多爬行者API (0138-Add-More-Creeper-API.patch)
- [ ] 生活实体射线追踪 (0143-Add-ray-tracing-methods-to-LivingEntity.patch)
- [ ] 暴露玩家攻击冷却 (0144-Expose-attack-cooldown-methods-for-Player.patch)
- [ ] 改进死亡事件 (0145-Improve-death-events.patch)
- [ ] 生物寻路API (0147-Mob-Pathfinding-API.patch)
- [ ] 生活实体获取目标实体 (0155-Add-LivingEntity-getTargetEntity.patch)
- [ ] 海龟API (0157-Turtle-API.patch)
- [ ] 观察者目标事件 (0158-Add-spectator-target-events.patch)
- [ ] 更多女巫API (0159-Add-more-Witch-API.patch)
- [ ] 更多僵尸API (0162-Add-more-Zombie-API.patch)
- [ ] 生物刷怪器API增强 (0176-Mob-Spawner-API-Enhancements.patch)
- [ ] 更多闪电API (0226-More-lightning-API.patch)
- [ ] 暴露生活实体受伤方向 (0229-Expose-LivingEntity-hurt-direction.patch)
- [ ] 僵尸破门API (0236-Zombie-API-breaking-doors.patch)
- [ ] 更多流浪商人API (0276-Add-more-WanderingTrader-API.patch)
- [ ] 幻翼和骷髅日光燃烧API (0281-Add-a-should-burn-in-sunlight-API-for-Phantoms-and-S.patch)
- [ ] 生物注视API (0286-Add-Mob-lookAt-API.patch)
- [ ] 更多附魔API (0285-More-Enchantment-API.patch)
- [ ] 生活实体清除活跃物品 (0224-Add-LivingEntity-clearActiveItem.patch)
- [ ] 更多末影龙战斗API (0433-More-DragonBattle-API.patch)
- [ ] 实体潜行API (0399-Add-Sneaking-API-for-Entities.patch)
- [ ] 飞行坠落伤害API (0402-Flying-Fall-Damage-API.patch)
- [ ] 可剪切API (0410-Add-Shearable-API.patch)
- [ ] 生物经验奖励API (0412-Add-Mob-Experience-reward-API.patch)
- [ ] 瞬态修饰符API (0414-Add-transient-modifier-API.patch)
- [ ] 移除所有活跃药水效果方法 (0416-Add-method-to-remove-all-active-potion-effects.patch)
- [ ] 实体记分板名称API (0426-API-for-an-entity-s-scoreboard-name.patch)
- [ ] 扩展姿势API (0431-Expand-Pose-API.patch)
- [ ] 三叉戟自定义伤害 (0435-Allow-trident-custom-damage.patch)

### 1.2.0-PRE10 方块/世界交互API
- [ ] 不使用快照获取BlockState (0074-API-to-get-a-BlockState-without-a-snapshot.patch)
- [ ] 暴露世界边界范围检查 (0053-Expose-WorldBorder-isInBounds-Location-check.patch)
- [ ] 位置转方块位置/中心位置 (0104-Location.toBlockLocation-toCenterLocation.patch)
- [ ] 扩展位置操作API (0128-Expand-Location-Manipulation-API.patch)
- [ ] 源方块构造器和变更方块数据 (0141-Add-source-block-constructor-and-getChangedBlockData.patch)
- [ ] 改进方块breakNaturally API (0181-Improve-Block-breakNaturally-API.patch)
- [ ] 方块声音组接口 (0177-Add-BlockSoundGroup-interface.patch)
- [ ] 附加方块材料API (0233-Additional-Block-Material-API-s.patch)
- [ ] 方块有效工具 (0263-Add-Block-isValidTool.patch)
- [ ] 更多有盖方块API (0291-More-Lidded-Block-API.patch)
- [ ] 多方块变更API (0340-Multi-Block-Change-API.patch)
- [ ] 方块tick API (0373-Block-Ticking-API.patch)
- [ ] BlockState获取掉落物 (0379-Add-getDrops-to-BlockState.patch)
- [ ] 移动活塞API (0387-Add-Moving-Piston-API.patch)
- [ ] 改进传送门事件 (0400-Improve-PortalEvents.patch)
- [ ] 方块爆炸状态 (0401-Add-exploded-block-state-to-BlockExplodeEvent-and-En.patch)

### 1.2.0-PRE11 库存/容器API
- [ ] 漏斗优化 (0087-Optimize-Hoppers.patch)
- [ ] 打开告示牌方法 (0094-Add-openSign-method-to-HumanEntity.patch)
- [ ] 库存移除任意槽位物品 (0139-Inventory-removeItemAnySlot.patch)
- [ ] 不使用方块快照获取库存持有者 (0199-Inventory-getHolder-method-without-block-snapshot.patch)
- [ ] 附加打开容器API (0213-Add-additional-open-container-api-to-HumanEntity.patch)
- [ ] 库存关闭 (0280-Inventory-close.patch)
- [ ] 暴露燃料和冶炼方法到熔炉库存 (0332-Expose-isFuel-and-canSmelt-methods-to-FurnaceInvento.patch)
- [ ] 熔炉配方使用API (0348-Furnace-RecipesUsed-API.patch)
- [ ] 库存打开事件标题覆盖 (0439-Add-titleOverride-to-InventoryOpenEvent.patch)

### 1.2.0-PRE12 配置/数据API
- [ ] 基础数据包API (0282-Add-basic-Datapack-API.patch)
- [ ] Paper注册表 (0245-Add-PaperRegistry.patch)
- [ ] 结构定位事件 (0246-Add-StructuresLocateEvent.patch)
- [ ] 缺失的原版标签 (0248-Added-missing-vanilla-tags.patch)
- [ ] 自定义药水混合 (0351-Custom-Potion-Mixes.patch)
- [ ] 配置雕塑传感器监听范围 (0349-Configurable-sculk-sensor-listener-range.patch)
- [ ] 缺失的方块数据最小最大值 (0350-Add-missing-block-data-mins-and-maxes.patch)
- [ ] 暴露熔炉矿车推力值 (0352-Expose-furnace-minecart-push-values.patch)
- [ ] 附魔等级API (0355-Add-enchantWithLevels-API.patch)
- [ ] 世界创建者保持生成加载 (0360-WorldCreator-keepSpawnLoaded.patch)
- [ ] 下落方块自动过期设置 (0363-FallingBlock-auto-expire-setting.patch)
- [ ] 键控猫类型 (0364-Keyed-Cat-Type.patch)
- [ ] 检查世界是否正在tick (0365-Add-method-isTickingWorlds-to-Bukkit.patch)
- [ ] 可命名横幅API (0367-Nameable-Banner-API.patch)
- [ ] 更多传送API (0369-More-Teleport-API.patch)
- [ ] 从LibraryLoader加载资源 (0375-Also-load-resources-from-LibraryLoader.patch)
- [ ] PersistentDataContainer字节数组序列化 (0376-Added-byte-array-serialization-deserialization-for-P.patch)
- [ ] 投射物源发射投射物消费者参数 (0377-Add-a-consumer-parameter-to-ProjectileSource-launchP.patch)
- [ ] 暴露YAML配置选项代码点限制 (0378-Expose-codepoint-limit-in-YamlConfigOptions-and-incr.patch)
- [ ] 改变床占用属性 (0382-Allow-changing-bed-s-occupied-property.patch)
- [ ] 装备槽便利方法 (0383-Add-EquipmentSlot-convenience-methods.patch)
- [ ] 生活实体挥手装备槽便利 (0384-Add-LivingEntity-swingHand-EquipmentSlot-convenience.patch)
- [ ] 纸张转储监听器命令 (0391-Add-paper-dumplisteners-command.patch)
- [ ] Tick时间单位 (0393-Add-Tick-TemporalUnit.patch)
- [ ] 摩擦API (0394-Friction-API.patch)
- [ ] 缺失的流体类型 (0396-Add-missing-Fluid-type.patch)
- [ ] 修复乐器 (0397-fix-Instruments.patch)
- [ ] 胜利屏幕API (0405-Win-Screen-API.patch)
- [ ] 修复刷怪蛋Meta获取设置生成类型 (0411-Fix-SpawnEggMeta-get-setSpawnedType.patch)
- [ ] 扩展玩家物品修补事件 (0413-Expand-PlayerItemMendEvent.patch)
- [ ] 正确移除实验性锻造台库存 (0415-Properly-remove-the-experimental-smithing-inventory-.patch)
- [ ] Folia调度器和拥有区域API (0417-Folia-scheduler-and-owned-region-API.patch)
- [ ] 玩家编辑告示牌事件 (0418-Add-event-for-player-editing-sign.patch)
- [ ] 告示牌获取可交互侧面 (0419-Add-Sign-getInteractableSideFor.patch)
- [ ] 修复封禁列表API (0420-Fix-BanList-API.patch)
- [ ] 客户端配方更新API (0422-API-for-updating-recipes-on-clients.patch)
- [ ] 修复自定义统计标准创建 (0424-Fix-custom-statistic-criteria-creation.patch)
- [ ] 玩家列表API (0428-Add-Listing-API-for-Player.patch)
- [ ] 暴露方块损坏事件点击面 (0429-Expose-clicked-BlockFace-during-BlockDamageEvent.patch)
- [ ] 修复船状态NPE (0430-Fix-NPE-on-Boat-getStatus.patch)
- [ ] 商人配方复制构造器 (0432-MerchantRecipe-add-copy-constructor.patch)
- [ ] 暴露方块构建事件手部 (0436-Expose-hand-during-BlockCanBuildEvent.patch)
- [ ] 限制setBurnTime到有效短值 (0437-Limit-setBurnTime-to-valid-short-values.patch)
- [ ] 正确检查空物品堆叠 (0440-Allow-proper-checking-of-empty-item-stacks.patch)

## ✅ 已完成

- [x] 1.0.8-PRE1：支持Velocity Modern转发（Port Mohist and PCF）
- [x] 1.0.8-PRE2：并入MPEM的部分优化项
- [x] 1.0.8-PRE2：支持Adventure库 (0005-Adventure.patch)
- [x] 1.0.8-PRE3：使用Paper方法优化初始化世界的速度
- [x] 1.0.8-RELEASE：更多i18n（打算用AI，我很懒）

### 🔄 已在现有版本中包含的API (避免重复)
- [x] 实体Origin API (0015-Entity-Origin-API.patch) - 已在1.1.0-PRE4包含
- [x] 实体添加/移除世界事件 (0032-Entity-AddTo-RemoveFrom-World-Events.patch) - 已在1.1.0-PRE4包含
- [x] 实体寻路事件 (0033-EntityPathfindEvent.patch) - 已在1.1.0-PRE3包含
- [x] 世界实用方法 (0118-Add-World.getEntity-UUID-API.patch) - 已在1.1.0-PRE4包含
- [x] 玩家基础资料API (0059-Basic-PlayerProfile-API.patch) - 已在1.1.0-PRE4包含
- [x] 玩家设置资料API (0091-Player.setPlayerProfile-API.patch) - 已在1.1.0-PRE4包含
- [x] Brigadier Mojang API (0295-Implement-Brigadier-Mojang-API.patch) - 已在1.1.0-PRE4包含